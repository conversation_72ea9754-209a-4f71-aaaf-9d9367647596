{**
 * Advance Country Language Currency
 *
 * <AUTHOR>
 * @copyright Sathi 2025
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License version 3.0
 * @version   1.2.2
 *
*}

<div class="modal" id="st-selector-modal">
	<div class="modal-dialog" role="document">
		<div class="modal-content" {if $image || $color}style="{if $image}background-image: url('{$image|escape:'htmlall':'UTF-8'}');background-size: contain;background-position: center;{elseif $color}background:{$color|escape:'htmlall':'UTF-8'};{/if}"{/if}>
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="{l s='Close' mod='stcountrylanguagecurrency'}">
					<span aria-hidden="true">
						<i class="material-icons">close</i>
					</span>
				</button>
				<h2 class="modal-title h6 text-sm-center" style="text-align:center;">
					{l s='Preference' mod='stcountrylanguagecurrency'}
				</h2>
			</div>
			<div class="modal-body st-modal-body">
				<form id="save_data" method="POST">
					<input type="hidden" name="change_localization" value="1" />
					<div class="form-group">
						<label class="form-control-label">
							{l s='Country' mod='stcountrylanguagecurrency'}
						</label>
						<select class="form-control st-select-country" name="id_country" >
							{foreach $countries as $country}
								<option value="{$country.id_country|escape:'htmlall':'UTF-8'}" {if $country.id_country
								eq $selectedCountry} selected {/if}>
									{$country.name|escape:'htmlall':'UTF-8'} 
								</option>
							{/foreach}
						</select>
					</div>
					<div class="form-group">
						<label class="form-control-label">
							{l s='Currency' mod='stcountrylanguagecurrency'}
						</label> 		
						<select class="form-control st-select-currency" name="id_currency" >
							{foreach $currencies as $currency}
								<option value="{$currency.id_currency|escape:'htmlall':'UTF-8'}"
								{if $currency.id_currency eq $selectedCurrency} selected {/if}> {$currency.iso_code|escape:'htmlall':'UTF-8'} {if $currency.iso_code != $currency.sign}{$currency.sign|escape:'htmlall':'UTF-8'}{/if} </option>
							{/foreach}
						</select>
					</div>					
					<div class="form-group">
						<label class="form-control-label">
							{l s='Language' mod='stcountrylanguagecurrency'}
						</label> 
						<select class="form-control st-select-language" name="id_language" >
							{foreach $languages as $language}
								<option value="{$language.id_lang|escape:'htmlall':'UTF-8'}" 
								{if $language.id_lang eq $selectedLanguage} selected{/if}>
									{$language.name|escape:'htmlall':'UTF-8'} 
								</option>
							{/foreach}
						</select>
					</div>
					<div class="form-group">
						<br/>
						<input type="submit" name="st-submit" value="{l s='Change' mod='stcountrylanguagecurrency'}" class="form-control btn btn-primary"/>  
					</div>
				</form>
			</div>
		</div>	 
	</div>	 
</div>	 