<?php
/**
 * Advance Country Language Currency
 *
 * <AUTHOR>
 * @copyright Sathi 2025
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License version 3.0
 * @version   1.2.2
 *
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class Stcountrylanguagecurrency extends Module
{	
    public $countryLangs = array(
        'AD' => array('ca'),
        'AE' => array('ar'),
        'AF' => array('fa','ps'),
        'AG' => array('en'),
        'AI' => array('en'),
        'AL' => array('sq'),
        'AM' => array('hy'),
        'AO' => array('pt'),
        'AQ' => array('en','es','fr','ru[1]'),
        'AR' => array('es'),
        'AS' => array('en','sm'),
        'AT' => array('de'),
        'AU' => array('en'),
        'AW' => array('nl','pap'),
        'AX' => array('sv'),
        'AZ' => array('az'),
        'BA' => array('bs','hr','sr'),
        'BB' => array('en'),
        'BD' => array('bn'),
        'BE' => array('nl','fr','de'),
        'BF' => array('fr'),
        'BG' => array('bg'),
        'BH' => array('ar'),
        'BI' => array('fr'),
        'BJ' => array('fr'),
        'BL' => array('fr'),
        'BM' => array('en'),
        'BN' => array('ms'),
        'BO' => array('es','qu','gn','ay'),
        'BQ' => array('nl'),
        'BR' => array('pt'),
        'BS' => array('en'),
        'BT' => array('dz'),
        'BV' => array('no'),
        'BW' => array('en','tn'),
        'BY' => array('be','ru'),
        'BZ' => array('en'),
        'CA' => array('en','fr'),
        'CC' => array('en'),
        'CD' => array('fr'),
        'CF' => array('fr','sg'),
        'CG' => array('fr'),
        'CH' => array('de','fr','it','rm'),
        'CI' => array('fr'),
        'CK' => array('en','rar'),
        'CL' => array('es'),
        'CM' => array('fr','en'),
        'CN' => array('zh-hans'),
        'CO' => array('es'),
        'CR' => array('es'),
        'CU' => array('es'),
        'CV' => array('pt'),
        'CW' => array('nl','en'),
        'CX' => array('en'),
        'CY' => array('el','tr'),
        'CZ' => array('cs'),
        'DE' => array('de'),
        'DJ' => array('fr','ar','so','aa'),
        'DK' => array('da'),
        'DM' => array('en'),
        'DO' => array('es'),
        'DZ' => array('ar'),
        'EC' => array('es'),
        'EE' => array('et'),
        'EG' => array('ar'),
        'EH' => array('ar','es','fr'),
        'ER' => array('ti','ar','en'),
        'ES' => array('ast','ca','es','eu','gl'),
        'ET' => array('am','om'),
        'FI' => array('fi','sv','se'),
        'FJ' => array('en'),
        'FK' => array('en'),
        'FM' => array('en'),
        'FO' => array('fo','da'),
        'FR' => array('fr'),
        'GA' => array('fr'),
        'GB' => array('gb','en','ga','cy','gd','kw'),
        'GD' => array('en'),
        'GE' => array('ka'),
        'GF' => array('fr'),
        'GG' => array('en'),
        'GH' => array('en'),
        'GI' => array('en'),
        'GL' => array('kl','da'),
        'GM' => array('en'),
        'GN' => array('fr'),
        'GP' => array('fr'),
        'GQ' => array('es','fr','pt'),
        'GR' => array('el'),
        'GS' => array('en'),
        'GT' => array('es'),
        'GU' => array('en','ch'),
        'GW' => array('pt'),
        'GY' => array('en'),
        'HK' => array('zh-hant','en'),
        'HM' => array('en'),
        'HN' => array('es'),
        'HR' => array('hr'),
        'HT' => array('fr','ht'),
        'HU' => array('hu'),
        'ID' => array('id', 'ind'),
        'IE' => array('en','ga'),
        'IL' => array('he'),
        'IM' => array('en'),
        'IN' => array('hi','en'),
        'IO' => array('en'),
        'IQ' => array('ar','ku'),
        'IR' => array('fa'),
        'IS' => array('is'),
        'IT' => array('it','de','fr'),
        'JE' => array('en'),
        'JM' => array('en'),
        'JO' => array('ar'),
        'JP' => array('ja'),
        'KE' => array('sw','en'),
        'KG' => array('ky','ru'),
        'KH' => array('km'),
        'KI' => array('en'),
        'KM' => array('ar','fr','sw'),
        'KN' => array('en'),
        'KP' => array('ko'),
        'KR' => array('ko','en'),
        'KW' => array('ar'),
        'KY' => array('en'),
        'KZ' => array('kk','ru'),
        'LA' => array('lo'),
        'LB' => array('ar','fr'),
        'LC' => array('en'),
        'LI' => array('de'),
        'LK' => array('si','ta'),
        'LR' => array('en'),
        'LS' => array('en','st'),
        'LT' => array('lt'),
        'LU' => array('lb','fr','de'),
        'LV' => array('lv'),
        'LY' => array('ar'),
        'MA' => array('fr','zgh','ar'),
        'MC' => array('fr'),
        'MD' => array('ro','ru','uk'),
        'ME' => array('srp','sr','hr','bs','sq'),
        'MF' => array('fr'),
        'MG' => array('mg','fr'),
        'MH' => array('en','mh'),
        'MK' => array('mk'),
        'ML' => array('fr'),
        'MM' => array('my'),
        'MN' => array('mn'),
        'MO' => array('zh-hant','pt'),
        'MP' => array('en','ch'),
        'MQ' => array('fr'),
        'MR' => array('ar','fr'),
        'MS' => array('en'),
        'MT' => array('mt','en'),
        'MU' => array('mfe','fr','en'),
        'MV' => array('dv'),
        'MW' => array('en','ny'),
        'MX' => array('es'),
        'MY' => array('ms'),
        'MZ' => array('pt'),
        'NA' => array('en','sf','de'),
        'NC' => array('fr'),
        'NE' => array('fr'),
        'NF' => array('en','pih'),
        'NG' => array('en'),
        'NI' => array('es'),
        'NL' => array('nl'),
        'NO' => array('nb','nn','no','se'),
        'NP' => array('ne'),
        'NR' => array('na','en'),
        'NU' => array('niu','en'),
        'NZ' => array('mi','en'),
        'OM' => array('ar'),
        'PA' => array('es'),
        'PE' => array('es'),
        'PF' => array('fr'),
        'PG' => array('en','tpi','ho'),
        'PH' => array('en','tl'),
        'PK' => array('en','ur'),
        'PL' => array('pl'),
        'PM' => array('fr'),
        'PN' => array('en','pih'),
        'PR' => array('es','en'),
        'PS' => array('ar','he'),
        'PT' => array('pt'),
        'PW' => array('en','pau','ja','sov','tox'),
        'PY' => array('es','gn'),
        'QA' => array('ar'),
        'RE' => array('fr'),
        'RO' => array('ro'),
        'RS' => array('sr','sr-Latn'),
        'RU' => array('ru'),
        'RW' => array('rw','fr','en'),
        'SA' => array('ar'),
        'SB' => array('en'),
        'SC' => array('fr','en','crs'),
        'SD' => array('ar','en'),
        'SE' => array('sv'),
        'SG' => array('zh-hans','en','ms','ta'),
        'SH' => array('en'),
        'SI' => array('sl'),
        'SJ' => array('no'),
        'SK' => array('sk'),
        'SL' => array('en'),
        'SM' => array('it'),
        'SN' => array('fr'),
        'SO' => array('so','ar'),
        'SR' => array('nl'),
        'ST' => array('pt'),
        'SS' => array('en'),
        'SV' => array('es'),
        'SX' => array('nl','en'),
        'SY' => array('ar'),
        'SZ' => array('en','ss'),
        'TC' => array('en'),
        'TD' => array('fr','ar'),
        'TF' => array('fr'),
        'TG' => array('fr'),
        'TH' => array('th'),
        'TJ' => array('tg','ru'),
        'TK' => array('tkl','en','sm'),
        'TL' => array('pt','tet'),
        'TM' => array('tk'),
        'TN' => array('ar','fr'),
        'TO' => array('en'),
        'TR' => array('tr'),
        'TT' => array('en'),
        'TV' => array('en'),
        'TW' => array('zh-hant'),
        'TZ' => array('sw','en'),
        'UA' => array('uk'),
        'UG' => array('en','sw'),
        'UM' => array('en'),
        'US' => array('en'),
        'UY' => array('es'),
        'UZ' => array('uz','kaa'),
        'VA' => array('it'),
        'VC' => array('en'),
        'VE' => array('es'),
        'VG' => array('en'),
        'VI' => array('en'),
        'VN' => array('vi'),
        'VU' => array('bi','en','fr'),
        'WF' => array('fr'),
        'WS' => array('sm','en'),
        'YE' => array('ar'),
        'YT' => array('fr'),
        'ZA' => array('en','af','st','tn','xh','zu'),
        'ZM' => array('en'),
        'ZW' => array('en','sn','nd'),
    );
    public $imageDir;
    public $configVars = array();

    public function __construct()
    {
        $this->name = "stcountrylanguagecurrency";
        $this->tab = "seo";
        $this->version = "1.2.2";
        $this->author = "Sathi";
        $this->need_instance = 0;
        $this->bootstrap = true;
        $this->imagename = "";
        $this->module_key = '65e035ab67b7cabb3bcc10c54082c459';

        parent::__construct();
		
        $this->imageDir = _PS_MODULE_DIR_. $this->name .'/views/img/';
        $this->displayName = $this->l('Auto Change Shop, Country, Currency and Language');
        $this->description = $this->l('This module allow to automatically change your shop, country, currency and language by customer geolocation.');
        $this->ps_versions_compliancy = array('min' => '1.6','max' => _PS_VERSION_);

        $this->configVars = array(
            'ST_AUTO_CHANGE_COUNTRY',
            'ST_AUTO_CHANGE_LANGUAGE',
            'ST_AUTO_CHANGE_CURRENCY',
            'ST_COUNTRY_CONFIGURATION',
            'ST_AUTO_CHANGE_SHOP',
            'ST_AUTO_CHANGE_SHOP_DATA',
            'ST_BACKGROUND',
            'ST_BACKGROUND_COLOR',
            'ST_BACKGROUND_IMAGE'
        );
    }

    public function install()
    {
        if (parent::install()
			&& $this->registerHook(
                array(
                    'displayHeader',
                    'displayNav',
                    'displayNav1',                    
                    'actionFrontControllerAfterInit',
                    'actionFrontControllerInitAfter',
                    'actionFrontControllerSetMedia',
                    'actionAdminControllerSetMedia',
					'actionFrontControllerInitBefore'
                )
            )
        ) {
            return true;
        }
        return false;
    }

    public function getConfigValue()
    {
        $value = Configuration::getMultiple($this->configVars);

        if ($value['ST_AUTO_CHANGE_SHOP_DATA']) {
            $value['ST_AUTO_CHANGE_SHOP_DATA'] = json_decode($value['ST_AUTO_CHANGE_SHOP_DATA'], true);
        }

        return $value;
    }

    public function getShopConfigValue($idShop = null)
    {
        $config = $this->getConfigValue();

        if ($idShop) {
            if (isset($config['ST_AUTO_CHANGE_SHOP_DATA'][$idShop])) {
                return $config['ST_AUTO_CHANGE_SHOP_DATA'][$idShop];
            }

            return array(
                'id_country' => 0,
                'id_currency' => 0,
                'id_language' => 0,
                'active' => 0
            );
        }

        return $config['ST_AUTO_CHANGE_SHOP_DATA'];
    }

    public function updateConfiguration()
    {
        foreach ($this->configVars as $var) {
            if ($var != 'ST_AUTO_CHANGE_SHOP_DATA') {
                Configuration::updateValue($var, Tools::getValue($var));
            }
        }
    }

    public function updateShopConfiguration($idShop, $params)
    {
        $val = json_decode(Configuration::get('ST_AUTO_CHANGE_SHOP_DATA'), true);
        $val[$idShop] = $params;

        Configuration::updateValue('ST_AUTO_CHANGE_SHOP_DATA', json_encode($val), true);
    }

    public function getBackgroundImage()
    {
        $imgname = 'background.jpg';
        if ($imgname && file_exists($this->imageDir.$imgname)) {
            $image = _PS_MODULE_DIR_. $this->name .'/views/img/'.$imgname;

            return ImageManager::thumbnail($image, $imgname, 100, 'jpg', true, true);
        }
    }

    public function postProcess()
    {
        if (Tools::isSubmit('submitConf')) {
            $this->updateConfiguration();

            if (isset($_FILES['ST_BACKGROUND_IMAGE'])
                && isset($_FILES['ST_BACKGROUND_IMAGE']['tmp_name'])
                && !empty($_FILES['ST_BACKGROUND_IMAGE']['tmp_name'])
            ) {
                if ($error = ImageManager::validateUpload($_FILES['ST_BACKGROUND_IMAGE'], 4000000)) {
                    $this->context->controller->errors[] = $error;
                } else {
                    $this->imagename = md5($_FILES['ST_BACKGROUND_IMAGE']['name']).'.jpg';

                    if (!move_uploaded_file($_FILES['ST_BACKGROUND_IMAGE']['tmp_name'], $this->imageDir . $this->imagename)) {
                        $this->context->controller->errors[] = $this->l('An error occurred while attempting to upload the file.');
                    }
                }
            }

            if (!$this->context->controller->errors) {
                if (Configuration::get('ST_BACKGROUND_IMAGE')) {
                    if ($this->imagename) {
                        copy($this->imageDir.$this->imagename, $this->imageDir.'background.jpg');
                        unlink($this->imageDir.$this->imagename);
                    }
                }

                Tools::redirectAdmin(
                    $this->context->link->getAdminLink('AdminModules').'&configure='.$this->name.'&conf=6'
                );
            }
        }

        if (Tools::isSubmit('submitStoreConf')) {
            if ($idShop = (int) Tools::getValue('id_shop')) {
                $this->updateShopConfiguration(
                    $idShop,
                    array(
                        'id_country' => Tools::getValue('id_country', 0),
                        'id_currency' => Tools::getValue('id_currency', 0),
                        'id_language' => Tools::getValue('id_language', 0),
                        'active' => Tools::getValue('active', 0)
                    )
                );
            }

            Tools::redirectAdmin(
                $this->context->link->getAdminLink('AdminModules').'&configure='.$this->name.'&conf=6'
            );
        }

        if (Tools::isSubmit('statusgeoShop') && $idShop = (int) Tools::getValue('id_shop')) {
            $params = $this->getShopConfigValue($idShop);

            $params['active'] = !$params['active'];

            $this->updateShopConfiguration(
                $idShop,
                $params
            );

            Tools::redirectAdmin(
                $this->context->link->getAdminLink('AdminModules').'&configure='.$this->name.'&conf=5'
            );
        }

        if (Tools::isSubmit('deleteBgImage')) {
            unlink($this->imageDir . 'background.jpg');

            Tools::redirectAdmin(
                $this->context->link->getAdminLink('AdminModules').'&configure='.$this->name.'&conf=7'
            );
        }
		
		if(Tools::isSubmit('btnGeocountry')){
			$geocountry = Tools::getValue('geocountry', array());
			Configuration::updateValue('ST_SET_CURRENCY_LANGUAGE_BY_COUNTRY', json_encode($geocountry), true);
			
		}
		if(Tools::isSubmit('btnResetGeocountry')){
			Configuration::deleteByName('ST_SET_CURRENCY_LANGUAGE_BY_COUNTRY');
		}
    }

    public function getContent()
    {
        $this->postProcess();

        if (Tools::isSubmit('updategeoShop') && $idShop = (int) Tools::getValue('id_shop')) {
            return $this->getShopForm($idShop);
        }

        return $this->getCommonForm() . $this->renderList() . $this->setGeoCountryForm();
    }

    public function getCommonForm()
    {
        $fields_form = array(
            'form' => array(
                'legend' => array(
                    'title' => $this->l('Settings'),
                    'icon' => 'icon-cogs'
                ),
                'input' => array(
                    array(
                        'type' => 'switch',
                        'label' => $this->l('Auto Change Country'),
                        'name' => 'ST_AUTO_CHANGE_COUNTRY',
                        'values' => array(
                            array(
                                'id' => 'type_switch_on',
                                'value' => 1,
                            ),
                            array(
                                'id' => 'type_switch_off',
                                'value' => 0,
                            ),
                        )
                    ),
                    array(
                        'type' => 'switch',
                        'label' => $this->l('Auto Change Currency'),
                        'name' => 'ST_AUTO_CHANGE_CURRENCY',
                        'values' => array(
                            array(
                                'id' => 'type_switch_on',
                                'value' => 1,
                            ),
                            array(
                                'id' => 'type_switch_off',
                                'value' => 0,
                            ),
                        )
                    ),
                    array(
                        'type' => 'switch',
                        'label' => $this->l('Auto Change Language'),
                        'name' => 'ST_AUTO_CHANGE_LANGUAGE',
                        'values' => array(
                            array(
                                'id' => 'type_switch_on',
                                'value' => 1,
                            ),
                            array(
                                'id' => 'type_switch_off',
                                'value' => 0,
                            ),
                        )
                    ),
					array(
                        'type' => 'switch',
                        'label' => $this->l('Country Configuration'),
                        'name' => 'ST_COUNTRY_CONFIGURATION',
						'desc' => $this->l('GeoCountry configuration will only work if the above Auto Change Country, Auto Change Currency and Auto Change Language buttons are on.'),
                        'values' => array(
                            array(
                                'id' => 'type_switch_on',
                                'value' => 1,
                            ),
                            array(
                                'id' => 'type_switch_off',
                                'value' => 0,
                            ),
                        )
                    ),
                    array(
                        'type' => 'select',
                        'label' => $this->l('Background'),
                        'name' => 'ST_BACKGROUND',
                        'options' => array(
                            'query' => array(
                                array(
                                    'id' =>	'background-color',
                                     'name' => $this->l('Background Color')
                                ),
                                array(
                                    'id' => 'background-image',
                                    'name' => $this->l('Background Image')
                                )
                            ),
                            'id' => 'id',
                            'name' => 'name',
                        ),
                    ),
                    array(
                        'type' => 'color',
                        'label' => $this->l('Background Color'),
                        'name' => 'ST_BACKGROUND_COLOR',
                        'form_group_class' => 'ST_BACKGROUND_background-color_element hide',
                    ),
                    array(
                        'type' => 'file',
                        'label' => $this->l('Background Image'),
                        'name' => 'ST_BACKGROUND_IMAGE',
                        'image' => $this->getBackgroundImage(),
                        'delete_url' => $this->context->link->getAdminLink('AdminModules').'&configure='.$this->name.'&deleteBgImage=1',
                        'form_group_class' => 'ST_BACKGROUND_background-image_element hide',
                    ),
                ),
                'submit' => array(
                    'title' => $this->l('Save'),
                )
            ),
        );

        if (Shop::isFeatureActive()) {
            array_splice(
                $fields_form['form']['input'],
                0,
                0,
                array(
                    array(
                        'type' => 'switch',
                        'label' => $this->l('Auto Change Shop'),
                        'name' => 'ST_AUTO_CHANGE_SHOP',
                        'values' => array(
                            array(
                                'id' => 'type_switch_on',
                                'value' => 1,
                            ),
                            array(
                                'id' => 'type_switch_off',
                                'value' => 0,
                            ),
                        )
                    )
                )
            );
        }

        $helper = new HelperForm();
        $helper->table = $this->table;
        $helper->module = $this;
        $helper->submit_action = 'submitConf';
        $helper->currentIndex = $this->context->link->getAdminLink('AdminModules', false).'&configure='.$this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');

        $helper->tpl_vars = array(
            'fields_value' => $this->getConfigValue(),
            'languages' => $this->context->controller->getLanguages(),
            'id_language' => $this->context->language->id
        );

        return $helper->generateForm(array($fields_form));
    }

    public function getShopForm($idShop)
    {
        $shop = new Shop($idShop, $this->context->language->id);
        $countries = Country::getCountries($this->context->language->id, true);
        $currencies = Currency::getCurrencies(false, true, true);
        foreach ($currencies as &$currency) {
            $currency['name'] .= ' (' . $currency['iso_code'];
            if (isset($currency['sign'])) {
                $currency['name'] .= ' ' . $currency['sign'];
            }

            $currency['name'] .= ')';
        }
        $languages = Language::getLanguages(true, $idShop);

        $fields_form = array(
            'form' => array(
                'legend' => array(
                    'title' => sprintf($this->l('Edit %s'), $shop->name),
                    'icon' => 'icon-cogs'
                ),
                'input' => array(
                    array(
                        'type' => 'hidden',
                        'name' => 'id_shop'
                    ),
                    array(
                        'type' => 'select',
                        'name' => 'id_country',
                        'label' => $this->l('Country'),
                        'class' => 'fixed-width-xxl',
                        'options' => array(
                            'query' => $countries,
                            'id' => 'id_country',
                            'name' => 'name',
                            'default' => array(
                                'value' => '',
                                'label' => $this->l('Default')
                            )
                        )
                    ),
                    array(
                        'type' => 'select',
                        'name' => 'id_currency',
                        'label' => $this->l('Currency'),
                        'class' => 'fixed-width-xxl',
                        'options' => array(
                            'query' => $currencies,
                            'id' => 'id_currency',
                            'name' => 'name',
                            'default' => array(
                                'value' => '',
                                'label' => $this->l('Default')
                            )
                        )
                    ),
                    array(
                        'type' => 'select',
                        'name' => 'id_language',
                        'label' => $this->l('Language'),
                        'class' => 'fixed-width-xxl',
                        'options' => array(
                            'query' => $languages,
                            'id' => 'id_lang',
                            'name' => 'name',
                            'default' => array(
                                'value' => '',
                                'label' => $this->l('Default')
                            )
                        )
                    ),
                    array(
                        'type' => 'switch',
                        'label' => $this->l('Active'),
                        'name' => 'active',
                        'values' => array(
                            array(
                                'id' => 'type_switch_on',
                                'value' => 1,
                            ),
                            array(
                                'id' => 'type_switch_off',
                                'value' => 0,
                            ),
                        )
                    ),
                ),
                'submit' => array(
                    'title' => $this->l('Save'),
                )
            ),
        );

        $helper = new HelperForm();
        $helper->table = $this->table;
        $helper->module = $this;
        $helper->submit_action = 'submitStoreConf';
        $helper->currentIndex = $this->context->link->getAdminLink('AdminModules', false).'&configure='.$this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');

        $shopValues = $this->getShopConfigValue($idShop);

        $shopValues['id_shop'] = $idShop;

        $helper->tpl_vars = array(
            'fields_value' => $shopValues,
            'languages' => $this->context->controller->getLanguages(),
            'id_language' => $this->context->language->id
        );

        return $helper->generateForm(array($fields_form));
    }

    protected function renderList()
    {
        $shops = Shop::getShops();

        $countries = Country::getCountries($this->context->language->id, true);
        $currencies = Currency::getCurrencies(false, true, true);
        $languages = Language::getLanguages();

        $names = array();
        foreach ($shops as $shop) {
            $names[$shop['id_shop']] = $shop['name'];
        }

        $countrys = array();
        foreach ($countries as $country) {
            $countrys[$country['id_country']] = $country['name'];
        }

        $currencys = array();
        foreach ($currencies as $currency) {
            $currency['name'] .= ' (' . $currency['iso_code'];
            if (isset($currency['sign'])) {
                $currency['name'] .= ' ' . $currency['sign'];
            }

            $currency['name'] .= ')';

            $currencys[$currency['id_currency']] = $currency['name'];
        }

        $langs = array();
        foreach ($languages as $language) {
            $langs[$language['id_lang']] = $language['name'];
        }

        $fieldsList = array(
            'id_shop' => array(
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ),
            'name' => array(
                'title' => $this->l('Shop'),
                'align' => 'text-center',
                'filter_key' => 'name',
                'type' => 'select',
                'list' => $names
            ),
            'id_country' => array(
                'title' => $this->l('Country'),
                'align' => 'center',
                'callback_object' => $this,
                'callback' => 'setCountry',
                'filter_key' => 'id_country',
                'type' => 'select',
                'list' => $countrys
            ),
            'id_currency' => array(
                'title' => $this->l('Currency'),
                'align' => 'center',
                'callback_object' => $this,
                'callback' => 'setCurrency',
                'filter_key' => 'id_currency',
                'type' => 'select',
                'list' => $currencys
            ),
            'id_language' => array(
                'title' => $this->l('Language'),
                'align' => 'center',
                'callback_object' => $this,
                'callback' => 'setLanguage',
                'filter_key' => 'id_language',
                'type' => 'select',
                'list' => $langs
            ),
            'active' => array(
                'title' => $this->l('Active'),
                'type' => 'bool',
                'align' => 'center',
                'active' => 'status',
            )
        );

        foreach ($shops as &$shop) {
            $shop = array_merge($shop, $this->getShopConfigValue($shop['id_shop']));
        }

        unset($shop);

        if (Tools::getValue('submitFiltergeoShop')) {
            foreach ($shops as $key => &$shop) {
                foreach ($fieldsList as $field => $params) {
                    $filterColumn = 'geoShopFilter_' . $field;

                    if (Tools::isSubmit($filterColumn)) {
                        $filterVal = Tools::getValue($filterColumn);

                        if ($filterVal != '' && $shop[$field] != $filterVal) {
                            unset($shops[$key]);
                            continue 2;
                        }
                    }
                }
            }
        } else {
            // Clear filter
            foreach (array_keys($fieldsList) as $field) {
                $filterColumn = 'geoShopFilter_' . $field;
                if (Tools::getValue($filterColumn) != '') {
                    Tools::redirectAdmin($this->context->link->getAdminLink('AdminModules').
                    '&configure='.$this->name);
                }
            }
        }

        if ($column = Tools::getValue('geoShopOrderby')) {
            $columnVals = array_column($shops, $column);
            if (Tools::getValue('geoShopOrderway') == 'desc') {
                $sortOrder = SORT_DESC;
            } else {
                $sortOrder = SORT_ASC;
            }

            array_multisort($columnVals, $sortOrder, $shops);
        }

        $helper = new HelperList();

        $helper->orderBy = Tools::getValue('geoShopOrderby', 'id_shop');
        $helper->orderWay = Tools::strtoupper(Tools::getValue('geoShopOrderway', 'ASC'));

        $helper->bulk_actions = false;

        $helper->shopLinkType = '';
        $helper->no_link = false;
        $helper->simple_header = false;
        $helper->actions = array('edit');
        $helper->module = $this;
        $helper->identifier = 'id_shop';
        $helper->title = $this->l('Shop Configuration');
        $helper->table = 'geoShop';
        $helper->token = Tools::getAdminTokenLite('AdminModules');
        $helper->currentIndex = AdminController::$currentIndex.'&configure='.$this->name;

        return $helper->generateList($shops, $fieldsList);
    }

    public function hookDisplayNav()
    {
        $country = new Country($this->context->country->id, $this->context->language->id);

        $this->context->smarty->assign(
            array(
                'country_name' => $country->name
            )
        );
        return $this->display($this->name, 'modalbutton.tpl');
    }

    public function hookDisplayNav1()
    {
        return $this->hookDisplayNav();
    }

    public function getRemoteData($host)
    {
        return Tools::file_get_contents($host);
    }

    public function getIdlangByCountryIso($countryIso)
    {
        $idLang = 0;
        $shopLangs = array();
        if (version_compare(_PS_VERSION_, '*******', '>')) {
            $shopLangs = Language::getIDs(true, (int)$this->context->shop->id);
        } else {
            $shopLangsAll = Language::getLanguages(true, (int)$this->context->shop->id);
            foreach ($shopLangsAll as $lang) {
                $shopLangs[] = $lang['id_lang'];
            }
        }

        if (Validate::isLanguageIsoCode($countryIso)) {
            $idLang = Language::getIdByIso($countryIso);
        }
        if (!$idLang && isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
            $browserLanguage = Tools::substr($_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 2);

            if ($browserLanguage &&
                Configuration::get('PS_DETECT_LANG') &&
                $browserLanguage &&
                Language::getIdByIso($browserLanguage)
            ) {
                $idLang = Language::getIdByIso($browserLanguage);
            }
        }

        if ($idLang && in_array($idLang, $shopLangs)) {
            return $idLang;
        }

        return Configuration::get('PS_DETECT_LANG');
    }
	
    public function getGeolocation($ip = '')
    {
        if (!$ip) {
            $ip = Tools::getRemoteAddr();
        }

        /*
        https://ipinfo.io/*************/geo
        {
           "ip": "*************",
           "hostname": "dmagentva.dotcom-monitor.com",
           "city": "Washington",
           "region": "Washington, D.C.",
           "country": "US",
           "loc": "38.8951,-77.0364",
           "org": "AS14361 HopOne Internet Corporation",
           "postal": "20004",
           "timezone": "America/New_York",
           "readme": "https://ipinfo.io/missingauth"
        }
        */

        $response = $this->getRemoteData(
            'https://ipinfo.io/'.$ip.'/geo'
        );

        if ($response) {
            $data = json_decode($response, true);
            if (isset($data['country'])) {
                return array(
                    "ip" => $data['ip'],
                    "id_country" => $this->getCountryIdByCountryCode($data['country']),
                    "id_currency" => $this->getCurrencyIdFromCountryCode($data['country']),
                    "id_language" => $this->getIdlangByCountryIso($data['country'])
                );
            }
        }

        /*
        http://ip-api.com/json/*************
        {
            "status": "success",
            "country": "United States",
            "countryCode": "US",
            "region": "DC",
            "regionName": "District of Columbia",
            "city": "Washington",
            "zip": "20068",
            "lat": 38.9072,
            "lon": -77.0369,
            "timezone": "America/New_York",
            "isp": "HopOne Internet Corporation",
            "org": "HopOne Internet Corporation",
            "as": "AS14361 HopOne Internet Corporation",
            "query": "*************"
        }
        */

        $response = $this->getRemoteData(
            'http://ip-api.com/json/'.$ip
        );

        if ($response) {
            $data = json_decode($response, true);
            if (isset($data['country'])) {
                return array(
                    "ip" => $data['query'],
                    "id_country" => $this->getCountryIdByCountryCode($data['countryCode']),
                    "id_currency" => $this->getCurrencyIdFromCountryCode($data['countryCode']),
                    "id_language" => $this->getIdlangByCountryIso($data['countryCode'])
                );
            }
        }

        /*
        https://freegeoip.app/json/*************
        {
            "ip": "*************",
            "country_code": "US",
            "country_name": "United States",
            "region_code": "",
            "region_name": "",
            "city": "",
            "zip_code": "",
            "time_zone": "America/Chicago",
            "latitude": 37.751,
            "longitude": -97.822,
            "metro_code": 0
        }
        */
		$response = $this->getRemoteData('https://freegeoip.app/json/'.$ip);

		if ($response) {
			$data = json_decode($response, true);

			if (isset($data['country_code'])) {
				return array(
					"ip" => $data['ip'],
					"id_country" => $this->getCountryIdByCountryCode($data['country_code']),
					"id_currency" => $this->getCurrencyIdFromCountryCode($data['country_code']),
					"id_language" => $this->getIdlangByCountryIso($data['country_code'])
				);
			}
		}


        /*
        http://www.geoplugin.net/php.gp?ip=*************
        a:24:{s:17:"geoplugin_request";s:13:"*************";s:16:"geoplugin_status";i:206;s:15:"geoplugin_delay";s:3:"2ms";s:16:"geoplugin_credit";s:145:"Some of the returned
        data includes GeoLite data created by MaxMind, available from <a href=\'http://www.maxmind.com\'>http://www.maxmind.com</a>.";s:14:"geoplugin_city";s:0:"";s:16:"geoplugin_region";s:0:"";s:20:"geoplugin_regionCode";s:0:"";s:20:"geoplugin_regionName";s:0:"";s:18:"geoplugin_areaCode";s:0:"";s:17:"geoplugin_dmaCode";s:0:"";s:21:"geoplugin_countryCode";s:2:"US";s:21:"geoplugin_countryName";s:13:"United	States";s:14:"geoplugin_inEU";i:0;s:19:"geoplugin_euVATrate";b:0;s:23:"geoplugin_continentCode";s:2:"NA";s:23:"geoplugin_continentName";s:13:"North	America";s:18:"geoplugin_latitude";s:6:"37.751";s:19:"geoplugin_longitude";s:7:"-97.822";s:32:"geoplugin_locationAccuracyRadius";s:4:"1000";s:18:"geoplugin_timezone";s:15:"America/Chicago";s:22:"geoplugin_currencyCode";s:3:"USD";s:24:"geoplugin_currencySymbol";s:5:"&#36;";s:29:"geoplugin_currencySymbol_UTF8";s:1:"$";s:27:"geoplugin_currencyConverter";s:1:"1";}
        */
		
		$response = $this->getRemoteData(
            'http://www.geoplugin.net/php.gp?ip='.$ip
        );

        if ($response) {
            $data = json_decode($response, true);

            if (isset($data['geoplugin_countryName'])) {
                return array(
                    "ip" => $data['geoplugin_request'],
                    "id_country" => $this->getCountryIdByCountryCode($data['geoplugin_countryCode']),
                    "id_currency" => $this->getCurrencyIdFromCountryCode($data['geoplugin_currencyCode']),
                    "id_language" => $this->getIdlangByCountryIso($data['geoplugin_countryCode'])
                );
            }
        }

        /*
        https://api.ipbase.com/v2/info?apikey=sgiPfh4j3aXFR3l2CnjWqdKQzxpqGn9pX5b3CUsz&ip=*************
        {"data":{"timezone":{"id":"America\/New_York","current_time":"2025-06-29T03:03:26-04:00","code":"EDT","is_daylight_saving":true,"gmt_offset":-14400},"ip":"*************","type":"v4","connection":{"asn":14361,"organization":"HOPONE-GLOBAL","isp":"Hopone Internet Corporation"},"location":{"geonames_id":5128481,"latitude":41.1555290222168,"longitude":-73.99075317382812,"zip":"10956","continent":{"code":"NA","name":"North America","name_translated":"North America"},"country":{"alpha2":"US","alpha3":"USA","calling_codes":["+1"],"currencies":[{"symbol":"$","name":"US Dollar","symbol_native":"$","decimal_digits":2,"rounding":0,"code":"USD","name_plural":"US dollars"}],"emoji":"🇺🇸","ioc":"USA","languages":[{"name":"English","name_native":"English"}],"name":"United States","name_translated":"United States","timezones":["America\/New_York","America\/Detroit","America\/Kentucky\/Louisville","America\/Kentucky\/Monticello","America\/Indiana\/Indianapolis","America\/Indiana\/Vincennes","America\/Indiana\/Winamac","America\/Indiana\/Marengo","America\/Indiana\/Petersburg","America\/Indiana\/Vevay","America\/Chicago","America\/Indiana\/Tell_City","America\/Indiana\/Knox","America\/Menominee","America\/North_Dakota\/Center","America\/North_Dakota\/New_Salem","America\/North_Dakota\/Beulah","America\/Denver","America\/Boise","America\/Phoenix","America\/Los_Angeles","America\/Anchorage","America\/Juneau","America\/Sitka","America\/Metlakatla","America\/Yakutat","America\/Nome","America\/Adak","Pacific\/Honolulu"],"is_in_european_union":false},"city":{"name":"New City","name_translated":"New City"},"region":{"fips":"","alpha2":"","name":"New York","name_translated":"New York"}}}}
        */
        $response = $this->getRemoteData('https://api.ipbase.com/v2/info?apikey=sgiPfh4j3aXFR3l2CnjWqdKQzxpqGn9pX5b3CUsz&ip='.$ip);

        if ($response) {
            $data = json_decode($response, true);

            if (isset($data['data']['location']['country']['alpha2'])) {
                $countryCode = $data['data']['location']['country']['alpha2'];
                return array(
                    "ip" => $data['data']['ip'],
                    "id_country" => $this->getCountryIdByCountryCode($countryCode),
                    "id_currency" => $this->getCurrencyIdFromCountryCode($countryCode),
                    "id_language" => $this->getIdlangByCountryIso($countryCode)
                );
            }
        }

        /*
        https://db-ip.com/demo/home.php?s=*************
        {"status":"ok","demoInfo":{"ipAddress":"*************","continentCode":"NA","continentName":"North America","countryCode":"US","countryName":"United States","isEuMember":false,"currencyCode":"USD","currencyName":"Dollar","phonePrefix":"1","languages":["en-US","es-US","haw","fr"],"stateProvCode":"DC","stateProv":"District of Columbia","district":"Washington","city":"Washington D.C.","geonameId":4140963,"zipCode":"20068","latitude":38.9072,"longitude":-77.0369,"gmtOffset":-4,"timeZone":"America\/New_York","weatherCode":"USDC0001","asNumber":14361,"asName":"HOPONE-GLOBAL","isp":"HopOne Internet Corporation","usageType":"hosting","organization":"HopOne Internet Corporation","isCrawler":true,"crawlerName":"DMBrowser-UV","isProxy":false,"threatLevel":"low"}}
        */
        $response = $this->getRemoteData('https://db-ip.com/demo/home.php?s='.$ip);

        if ($response) {
            $data = json_decode($response, true);

            if (isset($data['demoInfo']['countryCode'])) {
                $countryCode = $data['demoInfo']['countryCode'];
                return array(
                    "ip" => $data['demoInfo']['ipAddress'],
                    "id_country" => $this->getCountryIdByCountryCode($countryCode),
                    "id_currency" => $this->getCurrencyIdFromCountryCode($countryCode),
                    "id_language" => $this->getIdlangByCountryIso($countryCode)
                );
            }
        }

        return array();
    }
    public function getCurrencyIdFromCountryCode($country_code)
    {
        static $currencyArray = array();
        if (isset($currencyArray[$country_code])) {
            return $currencyArray[$country_code];
        }
        $shopCurrency = array();
        $shopCurrencyAll = Currency::getCurrenciesByIdShop((int)$this->context->shop->id);
        foreach ($shopCurrencyAll as $currency) {
            $shopCurrency[] = $currency['id_currency'];
        }

        $response = $this->getRemoteData(
            'http://country.io/currency.json'
        );

        $response = json_decode($response, true);

        if (isset($response[$country_code])) {
            $currency = new Currency(Currency::getIdByIsoCode($response[$country_code]));

            if ($currency->id && $currency->active) {
                return $currencyArray[$country_code] = $currency->id;
            }
        }

        $country = new Country(
            Country::getByIso($country_code)
        );
        if ($country->id && $country->active) {
            return $currencyArray[$country_code] = $country->id_currency;
        }

        return Configuration::get('PS_CURRENCY_DEFAULT');
    }

    public function getCountryIdByCountryCode($countryCode)
    {
        $idCountry = Country::getByIso($countryCode);
        $countryObj = new Country($idCountry);
        if ($countryObj->id && $countryObj->active) {
            return $idCountry;
        }
        return Configuration::get('PS_COUNTRY_DEFAULT');
    }

    public function updateGeoinfo(
        $idCountry,
        $idCurrency,
        $idLanguage,
        $redirect = null
    ) {
        if ($idCountry && $idCountry != $this->context->country->id) {
            $country = new Country((int) $idCountry);
            if (is_object($country) && $country->id) {
                $this->context->cookie->id_country = $country->id;
            }
        }

        if ($idCurrency && $idCurrency != $this->context->currency->id) {
            $currency = Currency::getCurrencyInstance((int) $idCurrency);
            if (is_object($currency) && $currency->id && !$currency->deleted && $currency->isAssociatedToShop()) {
                $this->context->cookie->id_currency = (int) $currency->id;
            }
        }
        if ($idLanguage && $idLanguage != $this->context->language->id) {
            $language = new Language((int) $idLanguage);
            if (is_object($language) && $language->id) {
                $this->context->cookie->id_lang = (int) $language->id;
            }
        }

        $redirect = $this->getRedirectLink($idCountry, $idCurrency, $idLanguage);

        Tools::redirect($redirect);
    }

    public function getRedirectLink($idCountry, $idCurrency, $idLanguage)
    {
        $idShop = 0;

        if (Configuration::get('ST_AUTO_CHANGE_SHOP')) {
            if ($shops = $this->getShopConfigValue()) {
                foreach ($shops as $id => $shop) {
                    if ($shop['active']
                        && (!$shop['id_country'] || $shop['id_country'] == $idCountry)
                        && (!$shop['id_currency'] || $shop['id_currency'] == $idCurrency)
                        && (!$shop['id_language'] || $shop['id_language'] == $idLanguage)
                    ) {
                        $idShop = $id;
                        break;
                    }
                }
            }
        }

        $context = $this->context;
        $contextShopId = $context->shop->id;

        if ($idShop && $idShop !== $contextShopId) {
            $context->shop = new Shop($idShop, $idLanguage);
        }

        $url = $this->context->link->getLanguageLink($idLanguage, $context);

        $parsedUrl = parse_url($url);

        $urlParams = array();

        if (isset($parsedUrl['query'])) {
            parse_str($parsedUrl['query'], $urlParams);
        }

        $newParams = $urlParams;

        if ($idShop && $idShop !== $contextShopId) {
            $newParams = array_merge(
                $urlParams,
                array(
                    'change_localization' => 1,
                    'id_currency' => $idCurrency,
                    'id_country' => $idCountry
                )
            );
        } else {
            unset(
                $newParams['change_localization'],
                $newParams['id_currency'],
                $newParams['id_country'],
                $newParams['id_language'],
                $newParams['SubmitCurrency']
            );
        }

        $parseUrl =  sprintf(
            '%s://%s%s%s',
            $parsedUrl['scheme'],
            $parsedUrl['host'],
            isset($parsedUrl['port']) ? ':' . $parsedUrl['port'] : '',
            $parsedUrl['path']
           
        );
		$build_query = http_build_query($newParams);
		if($build_query){
			$parseUrl .= '?'. $build_query;
		}
		return $parseUrl;
		
    }
	
	public function setGeoCountryForm()
	{
		$geocountry = json_decode(Configuration::get('ST_SET_CURRENCY_LANGUAGE_BY_COUNTRY'), true);
		$this->context->smarty->assign(
            array(
                'countries' => Country::getCountries((int) $this->context->language->id, true),
                'currencies' => Currency::getCurrencies(),
                'languages' => Language::getLanguages($this->context->language->id),
				'selected' => $geocountry
            )
        );

        return $this->display($this->name, 'views/templates/admin/configure.tpl');
		
	}
	
	public function hookActionFrontControllerInitBefore()
	{
		if (Tools::isSubmit('change_localization')) {
			$this->context->controller->php_self = '';
		}
	}
	
	public function hookActionFrontControllerAfterInit()
	{
		$this->hookActionFrontControllerInitAfter();
	}
	
	public function hookActionFrontControllerInitAfter()
	{
		if (Tools::isSubmit('change_localization')) {
            $idCountry = Tools::getValue('id_country', $this->context->cookie->id_country);
            $idCurrency = Tools::getValue('id_currency', $this->context->currency->id);
            $idLanguage = Tools::getValue('id_language', $this->context->language->id);

            $this->context->cookie->id_country = $idCountry;

            $this->updateGeoinfo(
                $idCountry,
                $idCurrency,
                $idLanguage
            );
        }

        if (!isset($this->context->cookie->id_country)) {
            $this->context->cookie->id_country = $this->context->country->id;

            if (Configuration::get('ST_AUTO_CHANGE_COUNTRY')
                || Configuration::get('ST_AUTO_CHANGE_LANGUAGE')
                || Configuration::get('ST_AUTO_CHANGE_CURRENCY')
            ) {
                if ($geoLocation = $this->getGeolocation()) {
					if(Configuration::get('ST_COUNTRY_CONFIGURATION')){
						$geocountries = Configuration::get('ST_SET_CURRENCY_LANGUAGE_BY_COUNTRY');
						if($geocountries){
							$geocountries = json_decode(Configuration::get('ST_SET_CURRENCY_LANGUAGE_BY_COUNTRY'), true);
							
							$country_config = array();
							if(isset($geocountries[$geoLocation['id_country']])){
								$country_config = $geocountries[$geoLocation['id_country']];
							} elseif(isset($geocountries[0])){
								$country_config = $geocountries[0];
							}
							if($country_config){
								if($country_config['id_currency'] >= 0){
									$geoLocation['id_currency'] = $country_config['id_currency'];
								}
								if($country_config['id_language'] >= 0){
									$geoLocation['id_language'] = $country_config['id_language'];
								}
							}
						}
					}
                    if (!Configuration::get('ST_AUTO_CHANGE_COUNTRY')) {
                        $geoLocation['id_country'] = 0;
                    }
                    if (!Configuration::get('ST_AUTO_CHANGE_CURRENCY')) {
                        $geoLocation['id_currency'] = 0;
                    }
                    if (!Configuration::get('ST_AUTO_CHANGE_LANGUAGE')) {
                        $geoLocation['id_language'] = 0;
                    }

                    $this->updateGeoinfo(
                        $geoLocation['id_country'],
                        $geoLocation['id_currency'],
                        $geoLocation['id_language']
                    );
                }
            }
        }
		
        if ($this->context->cookie->id_country != $this->context->country->id) {
            $country = new Country(
                $this->context->cookie->id_country,
                $this->context->language->id
            );
            if (validate::isLoadedObject($country)) {
                $this->context->country = $country;
                $this->context->cookie->iso_code_country = strtoupper($country->iso_code);
            }
        }
	}
	
    public function hookDisplayHeader()
    {
        $countries = Country::getCountries((int) $this->context->language->id, true);
        $currencies = Currency::getCurrencies();
        $languages = Language::getLanguages($this->context->language->id);

        $image = '';
        if (Configuration::get('ST_BACKGROUND') == 'background-image' && file_exists($this->imageDir.'background.jpg')) {
            $image = Tools::getHttpHost(true).__PS_BASE_URI__.'modules/'.$this->name.'/views/img/background.jpg';
        }

        $this->context->smarty->assign(
            array(
                'image' => $image,
                'color' => Configuration::get('ST_BACKGROUND_COLOR'),
                'selectedCountry' => $this->context->country->id,
                'selectedCurrency' => $this->context->currency->id,
                'selectedLanguage' => $this->context->language->id,
                'countries' => $countries,
                'currencies' => $currencies,
                'languages' => $languages
            )
        );

        return $this->display($this->name, 'selector.tpl');
    }

    public function deleteConfigTable()
    {
        foreach ($this->configVars as $var) {
            Configuration::deleteByName($var);
        }
		Configuration::deleteByName('ST_SET_CURRENCY_LANGUAGE_BY_COUNTRY');
        @unlink($this->imageDir.'background.jpg');
        return true;
    }

    public function hookActionAdminControllerSetMedia()
    {
        $this->context->controller->addJS(_MODULE_DIR_.$this->name.'/views/js/common.js');
		
		$this->context->controller->addCSS($this->_path.'views/css/back.css');
    }

    public function hookActionFrontControllerSetMedia()
    {
        $this->context->controller->addJS(_MODULE_DIR_.$this->name.'/views/js/common.js');

        $this->context->controller->addCSS($this->_path.'views/css/selector.css');
    }

    public function uninstall()
    {
        if (parent::uninstall()
            && $this->deleteConfigTable()
        ) {
            return true;
        }
        return false;
    }

    public function setCountry($idCountry)
    {
        if ($idCountry) {
            return (new Country($idCountry, $this->context->language->id))->name;
        }
    }

    public function setCurrency($idCurrency)
    {
        if ($idCurrency) {
            $currency = new Currency($idCurrency, $this->context->language->id);

            $currency->name .= ' (' . $currency->iso_code;
            if (isset($currency->sign)) {
                $currency->name .= ' ' . $currency->sign;
            }

            $currency->name .= ')';

            return $currency->name;
        }
    }

    public function setLanguage($idLang)
    {
        if ($idLang) {
            return (new Language($idLang))->name;
        }
    }
	
}
