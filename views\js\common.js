/**
 * Advance Country Language Currency
 *
 * <AUTHOR>
 * @copyright Sathi 2025
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License version 3.0
 * @version   1.2.2
 *
 */
 
$(function(){
	/* Back office */
	$('#module_form').on('change', '#ST_BACKGROUND', function(){
		var elm = $(this).attr('name');
		var val = $(this).val();
		$('[class*="'+ elm +'"]').addClass('hide');
		$('.'+ elm + '_' + val + '_element').removeClass('hide');	
	});
	
	$('#module_form #ST_BACKGROUND').trigger('change');
	
	$('#module_form').on('change', '[name="ST_AUTO_CHANGE_SHOP"]', function(){
		if ($(this).val() == '1') {
            $('#form-geoShop').removeClass('hide');        
        } else {
            $('#form-geoShop').addClass('hide');  
        }
	});
	
	if (!$('#module_form #ST_AUTO_CHANGE_SHOP_off').length
		|| $('#module_form #ST_AUTO_CHANGE_SHOP_off').is(':checked')
	){
		$('#form-geoShop').addClass('hide'); 
	}

	$('[id="ST_COUNTRY_CONFIGURATION_on"]').on('click', function(){
		$('.stcountrylanguagecurrency').removeClass('hide');
	});
			
	$('[id="ST_COUNTRY_CONFIGURATION_off"]').on('click', function(){
		$('.stcountrylanguagecurrency').addClass('hide');
	});
	
	$('[id="ST_COUNTRY_CONFIGURATION_on"]').each(function(i, element) {
		if ($(element).is(':checked')){
			$('.stcountrylanguagecurrency').removeClass('hide');
		}
	});
	
	
	/* Front office */
	$('.st-modal-opener').on('click',function(){
		if(typeof $.uniform != 'undefined'){
			$.uniform.restore("#st-selector-modal select")
		}
		if (typeof $.fancybox != 'undefined') {
			$.fancybox({'href': '#st-selector-modal'});
		}else{
			$('#st-selector-modal').modal('show');
		}
	});
	
});