{**
 * Advance Country Language Currency
 *
 * <AUTHOR>
 * @copyright Sathi 2025
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License version 3.0
 * @version   1.2.2
 *
*}

<form class="stcountrylanguagecurrency hide" method="POST">
	<div class="st-panel-heading">
		<i class="icon-cogs"></i>
		{l s='Currency and language settings by country' mod='stcountrylanguagecurrency'}
	</div>
	<table class="table geocountry-table">
		<tr class="st-country-lang-currency">
			<th class="fixed-width-xs">
				<span class="title_box">{l s='Id' mod='stcountrylanguagecurrency'}</span>
			</th>
			<th class="fixed-width-xs">
				<span class="title_box">{l s='Country' mod='stcountrylanguagecurrency'}</span>
			</th>
			<th class="center fixed-width-xs">
				<span class="title_box">{l s='Currency' mod='stcountrylanguagecurrency'}</span>
			</th>
			<th class="center fixed-width-xs">
				<span class="title_box">{l s='Language' mod='stcountrylanguagecurrency'}</span>
			</th>
		</tr>
		{foreach $countries as $country}
			<tr class="st-country-lang-currency">
				<td class="fixed-width-xs">
					<span class="title_box">{$country.id_country|escape:'htmlall':'UTF-8'}</span>
				</td>
				<td class="fixed-width-xs">
					<span class="title_box">{$country.name|escape:'htmlall':'UTF-8'}</span>
				</td>
				<td class="fixed-width-xs">
					<select  name="geocountry[{$country.id_country|escape:'htmlall':'UTF-8'}][id_currency]">
						<option value="-1">{l s='Auto' mod='stcountrylanguagecurrency'}</option>
						<option value="0" {if isset($selected[$country.id_country].id_currency) && $selected[$country.id_country].id_currency == 0}selected{/if}>{l s='Default' mod='stcountrylanguagecurrency'}</option>
						{foreach $currencies as $currency}
							<option class="title_box"  value="{$currency.id|escape:'htmlall':'UTF-8'}" {if isset($selected[$country.id_country].id_currency) && $selected[$country.id_country].id_currency == $currency.id}selected{/if}>{$currency.name|escape:'htmlall':'UTF-8'}</option>
						{/foreach}
					</select>
				</td>
				<td class="fixed-width-xs">
					<select name="geocountry[{$country.id_country|escape:'htmlall':'UTF-8'}][id_language]">
						<option value="-1">{l s='Auto' mod='stcountrylanguagecurrency'}</option>
						<option value="0" {if isset($selected[$country.id_country].id_language) && $selected[$country.id_country].id_language == 0}selected{/if}>{l s='Default' mod='stcountrylanguagecurrency'}</option>
						{foreach $languages as $lang}
							<option class="title_box" value="{$lang.id_lang|escape:'htmlall':'UTF-8'}"{if isset($selected[$country.id_country].id_language) && $selected[$country.id_country].id_language == $lang.id_lang}selected{/if}>{$lang.name|escape:'htmlall':'UTF-8'}</option>
						{/foreach}
					</select>
				</td>
			</tr>
		{/foreach}
		<tr class="st-country-lang-currency">
			<td class="fixed-width-xs">0</td>
			<td class="fixed-width-xs">{l s='Any country' mod='stcountrylanguagecurrency'}</td>
			<td class="fixed-width-xs">
				<select  name="geocountry[0][id_currency]">
					<option value="-1">{l s='Auto' mod='stcountrylanguagecurrency'}</option>
					<option value="0" {if isset($selected[0].id_currency) && $selected[0].id_currency == 0}selected{/if}>{l s='Default' mod='stcountrylanguagecurrency'}</option>
					{foreach $currencies as $currency}
						<option class="title_box"  value="{$currency.id|escape:'htmlall':'UTF-8'}" {if isset($selected[0].id_currency) && $selected[0].id_currency == $currency.id}selected{/if}>{$currency.name|escape:'htmlall':'UTF-8'}</option>
					{/foreach}
				</select>
			</td>
			<td class="fixed-width-xs">
				<select name="geocountry[0][id_language]">
					<option value="-1">{l s='Auto' mod='stcountrylanguagecurrency'}</option>
					<option value="0" {if isset($selected[0].id_language) && $selected[0].id_language == 0}selected{/if}>{l s='Default' mod='stcountrylanguagecurrency'}</option>
					{foreach $languages as $lang}
						<option class="title_box" value="{$lang.id_lang|escape:'htmlall':'UTF-8'}"{if isset($selected[0].id_language) && $selected[0].id_language == $lang.id_lang}selected{/if}>{$lang.name|escape:'htmlall':'UTF-8'}</option>
					{/foreach}
				</select>
			</td>
		</tr>
	</table>
	<div class="panel-footer">
		<button class="btn btn-primary" name="btnGeocountry" value="1" type="submit"><i class="process-icon-save"></i>{l s='Save' mod='stcountrylanguagecurrency'}</button>
		{if isset($selected) && $selected}
			<button class="btn btn-default " name="btnResetGeocountry" value="1" type="submit">{l s='Reset' mod='stcountrylanguagecurrency'}</button>
		{/if}
	</div>
</form>
